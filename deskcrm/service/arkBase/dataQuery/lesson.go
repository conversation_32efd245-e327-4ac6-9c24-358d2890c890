package dataQuery

import (
	"deskcrm/api/assistantdeskgo"
	"deskcrm/api/dataproxy"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetLuData 获取课程学生的LU数据
func (s *Singleton) GetLuData(ctx *gin.Context, courseId int64, studentUid int64) (map[int64]*dataproxy.GetStudentLessonDataCommonResp, error) {
	if courseId == 0 || studentUid == 0 {
		return make(map[int64]*dataproxy.GetStudentLessonDataCommonResp), nil
	}

	fields, err := s.GetFields(ctx, DataSourceLu)
	if err != nil {
		return nil, err
	}

	client := dataproxy.NewClient()
	params := dataproxy.GetListByCourseIdsStudentUidsParam{
		CourseIds:   []int64{courseId},
		StudentUids: []int64{studentUid},
		Fields:      fields,
	}

	data, err := client.GetListByCourseIdsStudentUids(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetCommonLuDataByLessonStudents failed: %v", err)
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetStudentLessonDataCommonResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}

// GetLuData 获取课程学生的公共LU数据
func (s *Singleton) GetCommonLuData(ctx *gin.Context, lessonIds []int64, studentUid int64) (map[int64]*dataproxy.GetCommonLuResp, error) {
	if len(lessonIds) == 0 || studentUid == 0 {
		return make(map[int64]*dataproxy.GetCommonLuResp), nil
	}

	fields, err := s.GetFields(ctx, DataSourceCommonLu)
	if err != nil {
		return nil, err
	}

	client := dataproxy.NewClient()
	params := dataproxy.GetLuComonListByStudentLessonsParam{
		LessonIds:  lessonIds,
		StudentUid: studentUid,
		Fields:     fields,
	}

	data, err := client.GetLuComonListByStudentLessons(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetCommonLuDataByLessonStudents failed: %v", err)
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetCommonLuResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}

// GetExamRelationData 获取试卷绑定数据
func (s *Singleton) GetExamRelationData(ctx *gin.Context, bindIds []int64, bindType int64, relationTypes []int64) (map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp, error) {
	if len(bindIds) == 0 || bindType == 0 {
		return make(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp), nil
	}

	fields, err := s.GetFields(ctx, DataSourceExamRelation)
	if err != nil {
		return nil, err
	}

	client := dataproxy.NewClient()

	params := dataproxy.GetListByBindIdsBindTypeRelationTypesCommonParam{
		BindIds:       bindIds,
		BindType:      bindType,
		RelationTypes: relationTypes,
		Fields:        fields,
	}

	data, err := client.GetListByBindIdsBindTypeRelationTypes(ctx, params)
	if err != nil {
		zlog.Warnf(ctx, "GetExamRelationData failed: %v", err)
		return nil, err
	}

	result := make(map[int64]map[int64]*dataproxy.GetListByBindIdsBindTypeRelationTypesCommonResp)
	for _, item := range data {
		result[item.BindId][item.RelationType] = item
	}

	return result, nil
}

func (s *Singleton) GetLearnReportByClueGradeIds(ctx *gin.Context, clueIdGradeIdMap map[string]int64) (getLessonListResp map[string]assistantdeskgo.LearnReportInfo, err error) {
	leadsInfoMap, err := assistantdeskgo.NewClient().GetLearnReportByClueGradeIds(ctx, clueIdGradeIdMap)
	if err != nil {
		zlog.Warnf(ctx, "GetPrivateLeadsData failed: %s", err.Error())
		return leadsInfoMap, nil
	}
	return leadsInfoMap, nil
}

// GetLessonDataByLessonIds 获取章节数据
func (s *Singleton) GetLessonDataByLessonIds(ctx *gin.Context, lessonIds []int64) (map[int64]*dataproxy.GetLessonDataByLessonIdsResp, error) {
	if len(lessonIds) == 0 {
		return make(map[int64]*dataproxy.GetLessonDataByLessonIdsResp), nil
	}

	fields, err := s.GetFields(ctx, DataSourceLesson)
	if err != nil {
		return nil, err
	}

	data, err := dataproxy.NewClient().GetLessonDataByLessonIds(ctx, dataproxy.GetLessonDataByLessonIdsParam{
		LessonIds: lessonIds,
		Fields:    fields,
	})
	if err != nil {
		return nil, err
	}

	result := make(map[int64]*dataproxy.GetLessonDataByLessonIdsResp)
	for _, item := range data {
		result[item.LessonId] = item
	}

	return result, nil
}
