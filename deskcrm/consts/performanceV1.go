package consts

// PerformanceV1 接口相关常量定义
// 对应 PHP Service_Page_DeskV1_Student_PerformanceV1 的常量

// Tab 类型常量 - 数据标签页类型
const (
	TAB_CORE_DATA = "coreData" // 核心数据（默认）
	TAB_EXAM      = "examData" // 试卷数据
)

// 课程类型常量
const (
	COURSE_TYPE_TRADITIONAL = 1 // 传统课程
	COURSE_TYPE_LPC         = 2 // LPC课程
)

// 课程开始状态常量
const (
	LESSON_STATUS_NOT_STARTED = 0 // 未开课
	LESSON_STATUS_UPCOMING    = 1 // 即将开课
	LESSON_STATUS_IN_PROGRESS = 2 // 正在开课
	LESSON_STATUS_FINISHED    = 3 // 已结束
)

// 试卷绑定类型常量
const (
	BIND_TYPE_HOMEWORK = 1 // 作业绑定类型
)

// 年级类型常量（用于判断预习类型）
const (
	GRADE_TYPE_PRIMARY = 1 // 小学
	GRADE_TYPE_JUNIOR  = 2 // 初中
	GRADE_TYPE_SENIOR  = 3 // 高中
)

// 错误码常量
const (
	ERROR_PARAM_INVALID     = 10001 // 参数错误
	ERROR_LEADS_NOT_FOUND   = 10002 // 获取leadsId失败
	ERROR_COURSE_NOT_FOUND  = 10003 // 课程信息不存在
	ERROR_LESSON_NOT_FOUND  = 10004 // 章节信息不存在
	ERROR_DATA_QUERY_FAILED = 10005 // 数据查询失败
)

// 数据状态颜色常量
const (
	COLOR_GREEN  = "green"  // 绿色 - 正常/完成
	COLOR_RED    = "red"    // 红色 - 异常/未完成
	COLOR_YELLOW = "yellow" // 黄色 - 警告/部分完成
	COLOR_GRAY   = "gray"   // 灰色 - 禁用/不可用
	COLOR_ORANGE = "orange" // 橙色 - 部分完成/良好
)

// 预习考试类型常量（根据年级区分）
const (
	EXAM_TYPE_PRIMARY_PREVIEW = 5  // 小学预习考试类型 (exam5)
	EXAM_TYPE_JUNIOR_PREVIEW  = 13 // 初高中预习考试类型 (exam13)
)

// LU数据字段常量
const (
	LU_FIELD_ATTEND_DURATION         = "attendDuration"          // 到课时长
	LU_FIELD_PREVIEW_TOTAL_NUM       = "previewTotalNum"         // 预习总数
	LU_FIELD_PREVIEW_PARTICIPATE_NUM = "previewParticipateNum"   // 预习参与数
	LU_FIELD_PREVIEW_CORRECT_NUM     = "previewCorrectNum"       // 预习正确数
	LU_FIELD_IN_CLASS_HANDS_UP_NUM   = "in_class_hands_up_num"   // 课中举手次数
	LU_FIELD_IN_CLASS_VIDEO_LINK_NUM = "in_class_video_link_num" // 课中连麦次数
)

// 导出相关常量
const (
	EXPORT_FORMAT_CSV   = "csv"   // CSV格式导出
	EXPORT_FORMAT_EXCEL = "excel" // Excel格式导出
)

// iLab 相关常量
const (
	ILAB_GRADE_ID   = 3 // iLab 年级ID（高中）
	ILAB_SUBJECT_ID = 4 // iLab 学科ID（英语）
)

var AcceptGradeSlice = []int64{3, 4}
var AcceptSubjectSlice = []int64{4}

// iLab 等级映射常量
var ILAB_LEVEL_MAP = map[int]string{
	1: "优秀", // 优秀
	2: "良好", // 良好
	3: "一般", // 一般
}
