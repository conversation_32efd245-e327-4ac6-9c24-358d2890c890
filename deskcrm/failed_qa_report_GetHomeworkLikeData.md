# GetHomeworkLikeData 函数验证失败报告

## 函数名 (FuncName)
`GetHomeworkLikeData`

## 问题描述 (Description)
在对比 PHP 与 Go 的相似题字段（similarHomework）实现后，发现 Go 侧存在关键的数据链路错误，导致在部分输入下与 PHP 输出不一致：

1. 错误调用不存在的数据查询方法
   - 业务层调用了 `s.dataQueryPoint.GetInstanceData(ctx, "GetLessonList", ...)`，但 dataQuery 层并无 `GetLessonList` 方法。
   - 该调用在反射分发时将触发 panic 被 recover，随后返回空数据，造成后续逻辑依赖数据缺失。

2. 错误的 `GetHomeworkOpenInfo` 调用签名
   - 业务层调用 `GetHomeworkOpenInfo` 时传入了两个参数（`courseId` 与 `lessonList`）。
   - 实际 dataQuery 层的函数签名仅接受一个参数：`(ctx, courseId int64)`，函数内部会自行查询并构建 lesson 列表。
   - 这会导致反射调用参数数量不匹配，返回错误后 `homeworkOpenInfo` 被置为空 map，从而把“应当开启”的作业误判为未开启，输出 `['-','gray',0]`，与 PHP 不一致。

3. 其余逻辑基本一致
   - 等级/状态映射、颜色及点击态与 PHP 版本保持一致；LU 字段访问路径正确。

## 复现路径/代码片段 (Reproduce Steps/Code Snippet)
- PHP 核心逻辑（检查开启+绑定后渲染）：
<augment_code_snippet path="assistantdesk/models/service/page/deskv1/student/Performance.php" mode="EXCERPT">
````php
if (isset($this->homeworkIsOpenInfos[$currentLessonInfo['lessonId']]['isOpenHomework'])
    && ($this->homeworkIsOpenInfos[$currentLessonInfo['lessonId']]['isOpenHomework'] == self::HOMEWORK_OPEN)
    && $isBindHw) {
    if (in_array($correctStatus, AssistantDesk_Config::EXAM_CORRECT_STATUS_WAIT_SHOW_MAP)) {
        $row['similarHomework'][0] = AssistantDesk_Config::$examCorrectStatusMap[$correctStatus] ?? '-';
    } else {
        $row['similarHomework'][0] = Api_Das::$homeworkLevelMap[$correctLevel] ?? '暂无等级';
    }
}
````
</augment_code_snippet>

- Go 有问题的调用（错误的方法与签名）：
<augment_code_snippet path="deskcrm/service/arkBase/lessonDataFactory/lessonDataFunc/lesson.go" mode="EXCERPT">
````go
lessonList, err := s.dataQueryPoint.GetInstanceData(ctx, "GetLessonList", []interface{}{s.param.CourseID})
...
homeworkOpenInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkOpenInfo", []interface{}{s.param.CourseID, lessonList})
````
</augment_code_snippet>

- 正确的数据访问方式（建议修复）：
<augment_code_snippet path="deskcrm/service/arkBase/lessonDataFactory/lessonDataFunc/lesson.go" mode="EXCERPT">
````go
homeworkOpenInfo, err := s.dataQueryPoint.GetInstanceData(ctx, "GetHomeworkOpenInfo", []interface{}{s.param.CourseID})
if err != nil {
    homeworkOpenInfo = make(map[int64]jxexamui.HomeworkOpenInfo)
}
openInfo := homeworkOpenInfo.(map[int64]jxexamui.HomeworkOpenInfo)
````
</augment_code_snippet>

## 建议修复方案 (Suggested Fix)
- 移除业务层对 `GetLessonList` 的调用。
- 将 `GetHomeworkOpenInfo` 的调用参数修正为仅传入 `courseId`：`[]interface{}{s.param.CourseID}`。
- 其余逻辑保持不变。

## 影响评估
- 高风险：在“课程作业已开启且已绑定”的真实场景下，Go 现实现会错误地输出未开启状态（`['-','gray',0]`），与 PHP 不一致。
- 中风险：由于错误的反射调用，潜在引发性能与日志噪音。

## 验证建议
- 构造以下用例比对 PHP 与 Go：
  - A. 作业开启 + 已绑定 + LU 有 exam33 字段且 correct_status ∈ 等待展示集合 → 应输出订正状态文案。
  - B. 作业开启 + 已绑定 + LU 有 exam33 字段且 correct_level>0 → 应输出等级文案与颜色（S 为绿色，否则橙色）。
  - C. 未开启或未绑定 → 输出 `['-','gray',0]`。
- 修复后对上述用例逐一核对输出完全一致。
