package jxexamui

import (
	"deskcrm/helpers"
	"fmt"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {

	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()

	//validator
	helpers.InitValidator()

	// 初始化API客户端配置
	helpers.InitApiClient()

	helpers.InitRedis()
}

func TestClient_GetExamTypeByLessonIds(t *testing.T) {

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	lessonIds := []int64{544939, 544940, 544941}

	result, err := NewClient().GetExamTypeByLessonIds(ctx, lessonIds)
	assert.NoError(t, err)
	// 测试函数能正常调用即可，不检查具体返回值
	fmt.Printf("result: %v\n", result)
}

func TestClient_GetLevelByExamTypeUidLessonIds(t *testing.T) {

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	studentUid := int64(2135540273)
	examTypes := []int{BindTypeHomeworkIlab, BindTypePosttestMore, BindTypeTestInClass, BindTypeHomework}
	lessonIds := []int64{544939, 544940, 544941}

	result, err := NewClient().GetLevelByExamTypeUidLessonIds(ctx, studentUid, examTypes, lessonIds)
	assert.NoError(t, err)
	// 测试函数能正常调用即可，不检查具体返回值
	fmt.Printf("result: %v\n", result)
}

func TestClient_GetHomeworkOpenTime(t *testing.T) {

	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	courseLessonList := make([]map[string]map[string]LessonDetail, 0)
	courseLesson := make(map[string]map[string]LessonDetail)
	courseLesson["552973"] = make(map[string]LessonDetail)
	courseLesson["552973"]["544939"] = LessonDetail{
		StopTime: 1672502400,
	}
	courseLesson["552973"]["544940"] = LessonDetail{
		StopTime: 1672502400,
	}
	courseLessonList = append(courseLessonList, courseLesson)
	param := GetHomeworkOpenTimeReq{
		CourseIds: courseLessonList,
	}

	result, err := NewClient().GetHomeworkOpenTime(ctx, param)
	assert.NoError(t, err)
	// 测试函数能正常调用即可，不检查具体返回值
	fmt.Printf("result: %v\n", result)
}
