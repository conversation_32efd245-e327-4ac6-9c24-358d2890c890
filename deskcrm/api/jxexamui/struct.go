package jxexamui

// ExamTypeInfo 章节试卷类型信息
type ExamTypeInfo struct {
	Strategy   int `json:"strategy"`   // 绑定策略
	ILabLesson int `json:"iLabLesson"` // 是否为iLab课程
	Version    int `json:"version"`    // 版本号
}

// PreviewInfo 预习信息
type PreviewInfo struct {
	Status        int `json:"status"`        // 是否有预习 0没有 1有
	IsOpen        int `json:"isOpen"`        // 是否开启 0未开启 1开启
	PreviewStatus int `json:"previewStatus"` // 预习类型 1.pdf 2.习题 3.都有
}

type GetHomeworkOpenTimeReq struct {
	CourseIds []map[string]map[string]LessonDetail `json:"courseIds"`
}

type LessonDetail struct {
	StopTime int `json:"stopTime"` // 结束时间
}

// HomeworkOpenInfo 作业开启信息
type HomeworkOpenInfo struct {
	IsOpen    int   `json:"isOpen"`    // 是否开启 0未开启 1开启
	StartTime int64 `json:"startTime"` // 开始时间
	StopTime  int64 `json:"stopTime"`  // 结束时间
}

// iLab等级映射，对应PHP中的Api_ExamUI::$levelIlabMap
var LevelIlabMap = map[int]string{
	0: "",
	1: "优秀",
	2: "良好",
	3: "待提升",
}

// 试卷绑定类型常量，对应PHP中的Api_Exam::BIND_TYPE_*
const (
	BindTypePreview         = 5  // 课前预习 小学预习
	BindTypePosttestMore    = 13 // 初高中预习测试
	BindTypeHomeworkIlab    = 11 // iLab巩固练习
	BindTypeHomework        = 7  // 普通巩固练习
	BindTypeTestInClass     = 10 // 堂堂测
	BindTypePracticeInClass = 1  // 课中练习
)
