package dal

import (
	"deskcrm/api"
	"deskcrm/api/zbcore"
	"deskcrm/components"
	"deskcrm/conf"
	"deskcrm/consts"
	"deskcrm/util"
	"slices"
	"sync"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	CourseModule = "dal"
	CourseEntity = "course"
)

type CourseInfo struct {
	CourseId            int                   `json:"courseId" mapstructure:"courseId"`
	CourseName          string                `json:"courseName" mapstructure:"courseName"`
	CourseType          int                   `json:"courseType"`
	LessonList          map[string]LessonInfo `json:"lessonList" mapstructure:"lessonList"`
	ShortTraining       int64                 `json:"shortTraining" mapstructure:"shortTraining" `
	SkuId               int64                 `json:"skuId"`
	MainGradeId         int64                 `json:"mainGradeId"`
	MainSubjectId       int64                 `json:"mainSubjectId"`
	NewCourseType       int64                 `json:"newCourseType"`
	Year                int64                 `json:"year"`
	FirstLessonTime     int64                 `json:"firstLessonTime"`
	StartTime           int64                 `json:"startTime"`
	StopTime            int64                 `json:"stopTime"`
	LearnSeason         int64                 `json:"learnSeason"`
	Season              int64                 `json:"season"`
	LastLessonStopTime  int64                 `json:"lastLessonStopTime"`
	CpuId               int64                 `json:"cpuId"`
	OnlineFormatTimeAll string                `json:"onlineFormatTimeAll"`
	Source              int64                 `json:"source"` // 课程来源
	ServiceInfo         []Service             `json:"serviceInfo"`
}

// Service 服务信息结构体
type Service struct {
	ID        int64 `json:"id"`
	ServiceID int64 `json:"serviceId"`
	// ......
}

func getCourseAllFields() []string {
	return []string{
		"courseId",
		"courseName",
		"grades",
		"subjects",
		"courseType",
		"season",
		"numberPeriods",
		"learnSeason",
		"year",
		"firstLessonTime",
		"lastLessonStopTime",
		"status",
		"isInner",
		"onlineFormatTime",
		"onlineFormatTimeAll",
		"lessonCnt",
		"lessonCompleteCnt",
		"coreLessonCnt",
		"coreLessonCompleteCnt",
		"hasMaterial",
		"mainGradeId",
		"mainSubjectId",
		"vipClass",
		"finishTime",
		"lectureSendTime",
		"formatShow",
		"deleteTime",
		"deleteReason",
		"cpuId",
		"courseTags",
		"brandId",
		"content",
		"startTime",
		"stopTime",
		"shortTraining",
		"newCourseType",
		"classType",
		"bookVer",
		"source",
		"serviceInfo",
		"isOwnPackage",
		"skuId"}
}

func GetKVByCourseId(ctx *gin.Context, courseId int64) (map[string]CourseInfo, error) {
	app := conf.GetAppName()
	arrParams := map[string]interface{}{
		"courseIds":    []int64{courseId},
		"courseFields": getCourseAllFields(),
		"lessonFields": getLessonAllFields(),
	}
	var output map[string]map[string]interface{}

	if util.GrayConfig.IsGrayDalGoApiSwitch(ctx) == true { //命中灰度走dalgo接口
		arrHeader := zbcore.GetHeaders(dalGetCourseApi, CourseModule, CourseEntity, "getKV", false, app)
		apiResp, err := zbcore.PostDalGo(ctx, arrParams, arrHeader, &output)
		zlog.Debugf(ctx, "PostDalGo apiResp:%+v", apiResp)
		if err != nil {
			zlog.Errorf(ctx, "dalgo api error, err:%s", err.Error())
			return nil, err
		}
	} else {
		arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, CourseModule, CourseEntity, "getKV", false, app)
		apiResp, err := zbcore.PostDal(ctx, arrParams, arrHeader, &output)
		zlog.Debugf(ctx, "PostDal apiResp:%+v", apiResp)
		if err != nil {
			zlog.Errorf(ctx, "dal api error, err:%s", err.Error())
			return nil, err
		}
	}

	result := decodeCourseResponse(ctx, output)
	return result, nil
}

func decodeCourseResponse(ctx *gin.Context, data map[string]map[string]interface{}) map[string]CourseInfo {
	result := make(map[string]CourseInfo)
	for courseIdStr, courseMap := range data {
		if len(courseMap) == 0 {
			zlog.Warnf(ctx, "courseMap is empty, courseID:%s", courseIdStr)
			continue
		}

		var course = CourseInfo{}
		if err := api.DecodeInterface(ctx, courseMap, &course); err != nil {
			zlog.Warnf(ctx, "decodeInterface failed, courseId:%s, courseMap:%+v, err:%s", courseIdStr, courseMap, err)
			continue
		}

		result[courseIdStr] = course
	}

	return result
}

func getKVByCourseId(ctx *gin.Context, courseIds []int64) (map[string]CourseInfo, error) {
	app := conf.GetAppName()
	arrParams := map[string]interface{}{
		"courseIds":    courseIds,
		"courseFields": getCourseAllFields(),
		"lessonFields": getLessonAllFields(),
	}
	var output map[string]map[string]interface{}

	if util.GrayConfig.IsGrayDalGoApiSwitch(ctx) == true { //命中灰度走dalgo接口
		arrHeader := zbcore.GetHeaders(dalGetCourseApi, CourseModule, CourseEntity, "getKV", false, app)
		apiResp, err := zbcore.PostDalGo(ctx, arrParams, arrHeader, &output)
		zlog.Debugf(ctx, "PostDalGo apiResp:%+v", apiResp)
		if err != nil {
			zlog.Errorf(ctx, "dalgo api error, err:%s", err.Error())
			return nil, err
		}
	} else {
		arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, CourseModule, CourseEntity, "getKV", false, app)
		apiResp, err := zbcore.PostDal(ctx, arrParams, arrHeader, &output)
		zlog.Debugf(ctx, "PostDal apiResp:%+v", apiResp)
		if err != nil {
			zlog.Errorf(ctx, "dal api error, err:%s", err.Error())
			return nil, err
		}
	}

	result := decodeCourseResponse(ctx, output)
	return result, nil
}

const CourseConcurrencyMax = 50

func courseLessonBatch(ctx *gin.Context, courseIds []int64) map[string]CourseInfo {
	chunks := util.ChunkArrayInt64(courseIds, CourseConcurrencyMax)
	wg := &sync.WaitGroup{}
	ch := make(chan map[string]CourseInfo)
	for _, chunk := range chunks {
		wg.Add(1)
		go func(cids []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "courseLessonBatch panic err : %+v", r)
				}
			}()
			defer wg.Done()
			ret, err := getKVByCourseId(ctx, cids)
			if err != nil {
				zlog.Warnf(ctx, "courseLessonSingle failed, courseIds:%+v, err:%s", cids, err)
				return
			}

			ch <- ret
		}(chunk)
	}

	result := make(map[string]CourseInfo)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "courseLessonBatch panic err:%s", r)
			}
		}()
		defer colWg.Done()
		for singleRet := range ch {
			for k, v := range singleRet {
				result[k] = v
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result
}

func GetCourseLessonInfoByCourseIds(ctx *gin.Context, courseIds []int64) (map[string]CourseInfo, error) {
	if len(courseIds) == 0 {
		return nil, base.Error{ErrNo: 500, ErrMsg: "courseIds不能为空"}
	}

	if len(courseIds) > CourseConcurrencyMax {
		// 批量并发获取
		return courseLessonBatch(ctx, courseIds), nil
	} else {
		return getKVByCourseId(ctx, courseIds)
	}
}

// getKVByCourseIdsWithFields 根据课程ID和自定义字段获取课程章节信息
func getKVByCourseIdsWithFields(ctx *gin.Context, courseIds []int64, courseFields []string, lessonFields []string) (map[string]CourseInfo, error) {
	app := conf.GetAppName()
	arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, CourseModule, CourseEntity, "getKV", false, app)
	arrParams := map[string]interface{}{
		"courseIds":    courseIds,
		"courseFields": courseFields,
	}

	if len(lessonFields) > 0 {
		arrParams["lessonFields"] = lessonFields
	}

	var output map[string]map[string]interface{}
	apiResp, err := zbcore.PostDal(ctx, arrParams, arrHeader, &output)
	zlog.Debugf(ctx, "apiResp:%+v", apiResp)
	if err != nil {
		return nil, err
	}

	result := decodeCourseResponse(ctx, output)
	return result, nil
}

// courseLessonBatchWithFields 批量并发获取课程章节信息（自定义字段版本）
func courseLessonBatchWithFields(ctx *gin.Context, courseIds []int64, courseFields []string, lessonFields []string) map[string]CourseInfo {
	chunks := util.ChunkArrayInt64(courseIds, CourseConcurrencyMax)
	wg := &sync.WaitGroup{}
	ch := make(chan map[string]CourseInfo)
	for _, chunk := range chunks {
		wg.Add(1)
		go func(cids []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "courseLessonBatchWithFields panic err : %+v", r)
				}
			}()
			defer wg.Done()
			ret, err := getKVByCourseIdsWithFields(ctx, cids, courseFields, lessonFields)
			if err != nil {
				zlog.Warnf(ctx, "courseLessonBatchWithFields failed, courseIds:%+v, err:%s", cids, err)
				return
			}

			ch <- ret
		}(chunk)
	}

	result := make(map[string]CourseInfo)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "courseLessonBatchWithFields panic err:%s", r)
			}
		}()
		defer colWg.Done()
		for singleRet := range ch {
			for k, v := range singleRet {
				result[k] = v
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result
}

func GetCourseLessonInfoByCourseIdsAndFields(ctx *gin.Context, courseIds []int64, courseFields []string, lessonFields []string) (map[string]CourseInfo, error) {
	if len(courseIds) == 0 {
		return nil, nil
	}

	if len(courseIds) > CourseConcurrencyMax {
		// 批量并发获取
		return courseLessonBatchWithFields(ctx, courseIds, courseFields, lessonFields), nil
	} else {
		return getKVByCourseIdsWithFields(ctx, courseIds, courseFields, lessonFields)
	}
}

// GetCourseBaseByCourseIds 根据课程ID获取课程基础信息，不包含章节信息，支持批量处理，每批最多50个课程ID
func GetCourseBaseByCourseIds(ctx *gin.Context, courseIds []int64, fields []string) (map[int64]CourseInfo, error) {
	if len(courseIds) == 0 {
		return make(map[int64]CourseInfo), nil
	}

	// 如果字段为空，使用默认字段
	if len(fields) == 0 {
		fields = getCourseAllFields()
	}

	// 确保courseId在字段列表中
	hasCourseid := slices.Contains(fields, "courseId")
	if !hasCourseid {
		fields = append(fields, "courseId")
	}

	// 使用现有的批量获取方法
	courseMap, err := GetCourseLessonInfoByCourseIdsAndFields(ctx, courseIds, fields, []string{})
	if err != nil {
		return nil, err
	}

	// 转换返回格式：从map[string]CourseInfo转换为map[int64]CourseInfo
	result := make(map[int64]CourseInfo)
	for _, course := range courseMap {
		result[int64(course.CourseId)] = course
	}

	return result, nil
}

func IsLpcByCourse(ctx *gin.Context, courseInfo CourseInfo) bool {
	courseServiceType := consts.CourseServiceTypeOther
	if courseInfo.CourseId == 0 {
		courseServiceType = consts.CourseServiceTypeOther
	}

	serviceId := make([]int64, 0)
	for _, item := range courseInfo.ServiceInfo {
		serviceId = append(serviceId, item.ServiceID)
	}

	if components.Array.InArrayInt64(1164, serviceId) {
		courseServiceType = consts.CourseServiceTypeLX
	} else if components.Array.InArrayInt64(1160, serviceId) {
		courseServiceType = consts.CourseServiceTypeDXB
	}

	return courseServiceType == consts.CourseServiceTypeLX
}

func (s CourseInfo) CheckIsHx() bool {

	// 2020年之前的课程不是小英课程
	if s.Year < 2020 {
		return false
	}

	// 2022年及以后的课程取消小英样式（除非是跟课场景）
	if s.Year >= 2022 {
		return false
	}

	// 检查章节的serviceInfo，看是否包含小英直播间服务ID
	for _, lessonInfo := range s.LessonList {
		for _, service := range lessonInfo.ServiceInfo {
			if service.ServiceId == LittleEnglishLiveRoomServiceId {
				return true
			}
		}
	}

	return false
}
