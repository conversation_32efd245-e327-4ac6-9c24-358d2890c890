# PHP方法迁移方舟规则清单
| num | key | funcName | status | comment |
|------|--------|----------|----------|--------------|
| 1 | lessonId | GetLessonId | verified | ✅ 重新验证通过 - 详见verification_report_GetLessonId.md |
| 2 | type | GetType | verified | ✅ 验证通过 |
| 3 | playType | GetPlayType | verified | ✅ 验证通过 |
| 4 | inclassTime | GetInclassTime | verified | ✅ 验证通过 |
| 5 | stopTime | GetStopTime | verified | ✅ 验证通过 |
| 6 | lessonName | GetLessonName | verified | ✅ 验证通过 |
| 7 | startTime | GetStartTime | verified | ✅ 验证通过 | 
| 8 | preview | GetPreview | verified | ✅ 完全修复 - 格式、数组结构、isPreviewFinish逻辑 |
| 9 | attend | GetAttendData | verified | ✅ 验证通过 - 逻辑与PHP一致，正确处理LBP、请假、时长格式化 |
| 10 | playback | GetPlayback | verified | ✅ 验证通过 - 逻辑与PHP一致，正确处理t007Tag、录播、时长格式化 |
| 11 | playbackv1 | GetPlaybackOnlineTimeV1 | verified | ✅ 修复格式化逻辑 - 确保0值返回"0min"与PHP一致 |
| 12 | lbpAttendDuration | GetLbpAttendDuration | verified | ✅ 修复格式化逻辑 - 确保0值也格式化与PHP一致 |
| 13 | lbpAttendDurationOld | GetLbpAttendDurationOld | verified | ✅ 修复格式化逻辑 - 确保0值也格式化与PHP一致 |
| 14 | inclassTest | GetInclassTest | done |  |
| 15 | oralQuestion | GetOralQuestion | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配 |
| 16 | homework | GetHomeworkData | failed_qa | See: failed_qa_report_GetHomeworkData.md |
| 19 | exerciseAll | GetExerciseAllColumn | verified | ✅ 验证通过 - 灰度逻辑、数据格式化、边界条件完全一致 |
| 17 | similarHomework | GetHomeworkLikeData | failed_qa | See: failed_qa_report_GetHomeworkLikeData.md |
| 18 | exercise | GetExerciseColumn | done |  |
| 19 | exerciseAll | GetExerciseAllColumn | done |  |
| 20 | lbpInteractExam | GetLbpInteractExamColumn | verified | ✅ 验证通过 - 逻辑等价、数据源一致、输出格式匹配 |
| 21 | mixPlaybackInteract | GetMixPlaybackInteract | done |  |
| 22 | littleKidFudaoHomeworkStatus | GetLittleKidFudaoData |  |  |
| 23 | littleKidFudaoHomeworkLevel | GetLittleKidFudaoData |  |  |
| 24 | littleKidFudaoInteract | GetLittleKidInteractData |  |  |
| 25 | synchronousPractice | GetSynchronousPractice |  |  |
| 26 | hasCompositionReport | GetHasCompositionReportData |  |  |
| 27 | talk | GetTalk |  |  |
| 28 | score | GetScoreData |  |  |
| 29 | monthlyExamReport | GetMonthlyExamReportUrl |  |  |
| 30 | isInclassTeacherRoomAttend30minute | GetIsInclassTeacherRoomAttend30minute |  |  |
| 31 | isAttendFinish | GetIsAttendFinish |  |  |
| 32 | gjkAttendLessonLubo | GetGjkAttendLessonLubo |  |  |
| 33 | gjkCompleteLessonLubo | GetGjkCompleteLessonLubo |  |  |
| 34 | gjkLessonTag | GetGjkLessonTag |  |  |
| 35 | lpclessonName | GetLpcLessonName |  |  |
| 36 | teacherName | GetLpcTeacherName |  |  |
| 37 | attendStatus | GetLpcAttendStatus |  |  |
| 38 | finishStatus | GetLpcFinishStatus |  |  |
| 39 | playStatus | GetLpcPlayStatus |  |  |
| 40 | preView | GetLpcPreViewData |  |  |
| 41 | tangtangExamStat | GetLpcTangTangExamStatData |  |  |
| 42 | strengthPracticeStatus | GetLpcStrengthPracticeData |  |  |
| 43 | lessonReportUrl | GetLpcLessonReportData |  |  |
| 44 | deerEloquenceHomeworkLevel | GetDeerEloquenceHomeworkLevel |  |  |
| 45 | deerProgrammingHomeworkLevel | GetDeerProgrammingHomeworkLevel |  |  |
| 46 | deerLessonReportUrl | GetDeerLessonReport |  |  |
| 47 | deerLessonHomeWork | GetLessonHomeWork |  |  |
| 48 | zhiboLessonReportUrl | GetZhiboLessonReport |  |  |
