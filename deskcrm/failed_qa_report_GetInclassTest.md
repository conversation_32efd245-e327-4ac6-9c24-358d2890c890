# QA验证失败报告 - GetInclassTest

## 函数名 (FuncName)
GetInclassTest

## 问题描述 (Description)
Go 实现中**完全缺少小英课程（HX课程）的特殊处理逻辑**，导致小英课程的堂堂测显示格式与 PHP 完全不一致。

**核心问题：**
1. **缺少小英课程检查**：Go 代码没有调用 `CheckIsHx()` 方法来判断是否为小英课程
2. **缺少必要字段获取**：Go 代码没有获取小英课程需要的 `tangTangExamScore` 和 `isTangTangExamSubmit` 字段
3. **缺少小英课程显示逻辑**：Go 代码只实现了非小英课程的 "X|Y|Z" 格式，没有实现小英课程的得分显示格式

**影响范围：**
- 所有小英课程（2020-2021年的浣熊课程）的堂堂测显示都会不正确
- 小英课程应该显示 "X分" 或 "未提交"，但 Go 实现会显示 "X|Y|Z" 格式

## 复现路径/代码片段 (Reproduce Steps/Code Snippet)

### PHP Snippet (正确实现)
```php
private function getInclassTest(&$row, $currentLessonInfo) {
    // ... 获取基础数据 ...

    $row['inclassTest'] = ['-', 'gray', 1];

    // 检查是否为小英课程
    $isHxCourse = AssistantDesk_Tools::checkIsHx($this->courseId);

    if (!$isHxCourse) {
        // 非小英课程：显示 "X|Y|Z" 格式
        $row['inclassTest'][0] = sprintf(
            self::LESSON_EXERCISE_DETAIL,
            $lessonLuData['tangTangExamCorrectNum'],
            $lessonLuData['tangTangExamParticipateNum'],
            $tangTangToalNum
        );
        // 设置颜色逻辑...
    } else {
        // 小英课程：展示得分
        if ($lessonLuData['isTangTangExamSubmit']) {
            $row['inclassTest'][0] = sprintf('%s分', ($lessonLuData['tangTangExamScore'] / 10));
        } else if ($tangTangToalNum > 0) {
            $row['inclassTest'][0] = '未提交';
        }
    }

    // ILab 兼容逻辑...
}
```

### Go Snippet (有问题的实现)
```go
func (s *Format) GetInclassTest(ctx *gin.Context) (err error) {
    // 只获取了基础字段，缺少小英课程需要的字段
    if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{"tangTangExamCorrectNum", "tangTangExamParticipateNum"}) &&
        s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceExamRelation, []string{"bind_id", "relation_type", "bindStatus", "total_num"}) {
        return
    }

    // ... 获取数据 ...

    for _, lessonID := range s.param.LessonIDs {
        inclassTestArray := LessonDataArray("-", "gray", 1)

        // 获取学生堂堂测数据
        if lessonLuData, exists := luData[lessonID]; exists {
            correctNum := lessonLuData.TangTangExamCorrectNum
            participateNum := lessonLuData.TangTangExamParticipateNum

            // 问题：只实现了非小英课程的格式，没有检查是否为小英课程
            inclassTestArray[0] = fmt.Sprintf(dataQuery.LESSON_EXERCISE_DETAIL, correctNum, participateNum, tangTangTotalNum)

            // 颜色设置逻辑...
        }

        // ILab 兼容逻辑...
    }
}
```

## 建议修复方案 (Suggested Fix)

1. **添加小英课程检查逻辑**：
   ```go
   // 检查是否为小英课程
   isHxCourse := course.CheckIsHx()
   ```

2. **添加必要字段获取**：
   ```go
   if s.dataQueryPoint.BeforeAddFields(ctx, dataQuery.DataSourceLu, []string{
       "tangTangExamCorrectNum", "tangTangExamParticipateNum",
       "tangTangExamScore", "isTangTangExamSubmit"  // 添加小英课程需要的字段
   }) && ...
   ```

3. **实现小英课程的显示逻辑**：
   ```go
   if !isHxCourse {
       // 非小英课程：显示 "X|Y|Z" 格式
       inclassTestArray[0] = fmt.Sprintf(dataQuery.LESSON_EXERCISE_DETAIL, correctNum, participateNum, tangTangTotalNum)
       // 颜色设置...
   } else {
       // 小英课程：展示得分
       if lessonLuData.IsTangTangExamSubmit > 0 {
           inclassTestArray[0] = fmt.Sprintf("%d分", lessonLuData.TangTangExamScore/10)
       } else if tangTangTotalNum > 0 {
           inclassTestArray[0] = "未提交"
       }
   }
   ```

## 验证建议
修复后需要测试：
1. 非小英课程的显示格式保持不变
2. 小英课程正确显示得分格式
3. 小英课程未提交时正确显示"未提交"
4. ILab 兼容逻辑不受影响

## 严重程度
**高** - 核心业务逻辑错误，导致小英课程堂堂测数据展示完全错误